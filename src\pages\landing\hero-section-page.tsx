"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useEffect, useState } from "react";

const quotes = ["Your personalized roadmap to CP excellence"];

const HeroSection = () => {
  const [quote, setQuote] = useState("");

  useEffect(() => {
    setQuote(quotes[Math.floor(Math.random() * quotes.length)]);
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center px-4 md:px-6">
      <h1 className="text-4xl sm:text-5xl md:text-7xl  tracking-tight text-white mb-4 sm:mb-6">
        MyCPTrainer<br></br> Precision Over Randomness
      </h1>
      <p className="max-w-xs sm:max-w-xl md:max-w-3xl text-base sm:text-lg md:text-xl text-slate-300 mb-8 sm:mb-10 h-24 sm:h-16">
        {quote}
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Link href="/sheetscope">
          <Button
            variant="outline"
            className="py-3 px-6 rounded-lg border-2 border-slate-400 text-white hover:bg-slate-800 hover:border-slate-300 transition-colors"
          >
            Try for Free
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default HeroSection;
