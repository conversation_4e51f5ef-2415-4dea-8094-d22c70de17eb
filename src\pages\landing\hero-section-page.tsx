"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useState } from "react";

const HeroSection = () => {
  const [quote, setQuote] = useState("");

  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center px-4 md:px-6 animate-in fade-in duration-1000">
      {/* Decorative Element */}
      <div className="w-16 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full mb-8 opacity-80"></div>

      {/* Main Heading with Gradient */}
      <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-wide mb-4 leading-tight">
        <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
          MyCPTrainer
        </span>
      </h1>

      {/* Subheading */}
      <h2 className="text-lg sm:text-xl md:text-2xl font-light tracking-wider text-slate-300 mb-12 opacity-90">
        Precision Over Randomness
      </h2>

      {/* Call to Action */}
      <div className="group">
        <Link href="/sheetscope">
          <Button
            variant="outline"
            className="py-3 px-8 rounded-full border-2 border-purple-400/50 text-white hover:bg-purple-500/20 hover:border-purple-300 hover:scale-105 transition-all duration-300 font-medium backdrop-blur-sm"
          >
            Get Started
            <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">
              →
            </span>
          </Button>
        </Link>
      </div>

      {/* Bottom Decorative Element */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
        <div className="flex space-x-2 opacity-40">
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-150"></div>
          <div className="w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-300"></div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
