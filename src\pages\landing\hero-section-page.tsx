"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useEffect, useState } from "react";



const HeroSection = () => {
  const [quote, setQuote] = useState("");

  useEffect(() => {
    setQuote(quotes[Math.floor(Math.random() * quotes.length)]);
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center px-4 md:px-6">
      {/* Main Heading */}
      <h1 className="text-5xl sm:text-6xl md:text-8xl lg:text-9xl font-bold tracking-wider text-white mb-6 sm:mb-8 leading-tight">
        MyCPTrainer
      </h1>

      {/* Subheading */}
      <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-medium tracking-wide text-slate-200 mb-8 sm:mb-12 leading-relaxed">
        Precision Over Randomness
      </h2>

      {/* Description */}
      <p className="max-w-xs sm:max-w-xl md:max-w-3xl text-base sm:text-lg md:text-xl text-slate-300 mb-10 sm:mb-12 leading-relaxed">
        {quote}
      </p>

      {/* Call to Action */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Link href="/sheetscope">
          <Button
            variant="outline"
            className="py-3 px-8 rounded-lg border-2 border-slate-400 text-white hover:bg-slate-800 hover:border-slate-300 transition-all duration-300 font-medium text-lg"
          >
            Try for Free
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default HeroSection;
